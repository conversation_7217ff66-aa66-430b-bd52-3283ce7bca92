import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/device_info/i_device_info_service.dart';
import 'package:flutter_audio_room/services/package_info/i_package_info_service.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/services/websocket_service/domain/entities/websocket_entity.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/models/response.dart'
    as response;
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';

enum CloseCode {
  initial(0),
  normal(1),
  goingAway(1000),
  networkError(1005),
  ;

  const CloseCode(this.value);
  final int value;
}

/// WebSocket data source interface
abstract class WebSocketDataSource {
  /// 获取WebSocket URL
  String get url;

  /// 连接到WebSocket服务器
  Future<ResultWithData<void>> connect();

  /// 断开WebSocket连接
  Future<ResultWithData<void>> disconnect({required CloseCode closeCode});

  /// 消息流，用于接收服务器消息
  Stream<response.WebsocketResponse> get messageStream;

  /// 发送消息到WebSocket服务器
  Future<ResultWithData<void>> sendMessage(WebSocketMessageEntity message);

  /// 当前连接状态
  bool get isConnected;

  /// 开始心跳检测
  void startHeartbeat();

  /// 停止心跳检测
  void stopHeartbeat();

  /// 尝试刷新Token并重连
  Future<void> tryRefreshTokenAndReconnect();

  /// 重置重连状态，允许重新开始重连
  void resetReconnectionState();

  Future<void> dispose();
}

/// WebSocket data source implementation
class WebSocketDataSourceImpl implements WebSocketDataSource {
  WebSocketDataSourceImpl({
    required this.storageService,
    required String url,
  }) : _url = url {
    LogUtils.d('WebSocketDataSource created for URL: $_url',
        tag: 'WebSocketDataSourceImpl.constructor');
  }

  @override
  String get url => _url;

  final String _url;
  final StorageService storageService;
  WebSocket? _socket;
  bool _isConnected = false;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  final _messageController =
      StreamController<response.WebsocketResponse>.broadcast();

  // Configuration
  static const int heartbeatInterval = 30; // seconds
  static const int initialReconnectDelay = 1; // seconds
  static const int maxReconnectDelay = 15; // seconds
  static String heartbeatMessage = jsonEncode({'msg': 'ping'});

  int _reconnectAttempts = 0;
  DateTime? _lastHeartbeatResponse;
  Future<void>? _activeReconnectTask; // 当前活跃的重连任务
  Future<ResultWithData<void>>? _activeConnectTask; // 当前活跃的连接任务

  // 添加连接状态锁，确保连接操作的原子性
  bool _isConnecting = false;

  @override
  bool get isConnected => _isConnected;

  @override
  Future<ResultWithData<void>> connect() async {
    // 如果已有活跃的连接任务，等待其完成
    if (_activeConnectTask != null) {
      LogUtils.d('连接任务已在进行中，等待完成 - $_url',
          tag: 'WebSocketDataSourceImpl.connect');
      return await _activeConnectTask!;
    }

    // 如果已经连接，直接返回成功
    if (_isConnected) {
      LogUtils.d('WebSocket已连接，无需重复连接 - $_url',
          tag: 'WebSocketDataSourceImpl.connect');
      return Either.right(null);
    }

    // 创建新的连接任务
    _activeConnectTask = _performConnect();

    try {
      return await _activeConnectTask!;
    } finally {
      _activeConnectTask = null;
    }
  }

  Future<ResultWithData<void>> _performConnect() async {
    final result = await _establishConnection(closeCode: CloseCode.initial);

    // 如果连接失败，启动自动重连机制
    result.fold(
      (error) {
        LogUtils.w(
          'Initial connection failed, starting auto-reconnect: ${error.message}',
          tag: 'WebSocketDataSourceImpl._performConnect',
        );
        // 直接调用重连，避免Future.microtask可能导致的竞态条件
        // 使用unawaited确保不阻塞当前调用
        unawaited(tryRefreshTokenAndReconnect());
      },
      (_) {
        // 连接成功，重置重连计数
        _reconnectAttempts = 0;
      },
    );

    return result;
  }

  Future<ResultWithData<void>> _establishConnection({
    required CloseCode closeCode,
  }) async {
    try {
      // 检查是否正在连接中，避免并发连接
      if (_isConnecting) {
        LogUtils.d('WebSocket正在连接中，跳过重复连接请求',
            tag: 'WebSocketDataSourceImpl._establishConnection');
        return Either.right(null);
      }

      // 如果已经连接，直接返回成功
      if (_isConnected && _socket != null) {
        LogUtils.d('WebSocket已连接，跳过连接',
            tag: 'WebSocketDataSourceImpl._establishConnection');
        return Either.right(null);
      }

      // 设置连接状态锁
      _isConnecting = true;

      try {
        // 强制断开现有连接，确保没有残留连接
        LogUtils.d('强制断开现有连接以确保干净的连接状态',
            tag: 'WebSocketDataSourceImpl._establishConnection');
        await disconnect(closeCode: closeCode);

        // 等待一小段时间确保旧连接完全释放
        await Future.delayed(const Duration(milliseconds: 100));

        // 再次检查dispose状态
        if (isDisposing) {
          LogUtils.d('WebSocket正在dispose，取消连接尝试',
              tag: 'WebSocketDataSourceImpl._establishConnection');
          return Either.left(const AppException(
            statusCode: 1006,
            message: 'WebSocket is disposing',
            identifier: 'WS_DISPOSING',
          ));
        }

        LogUtils.i(
          'Attempting to connect to WebSocket: $_url',
          tag: 'WebSocketDataSourceImpl._establishConnection',
        );

        final accessToken = storageService.accessToken;
        if (accessToken.isEmpty) {
          return Either.left(const AppException(
            statusCode: 401,
            message: 'No session info available',
            identifier: 'WS_NO_SESSION_INFO',
          ));
        }

        final timezone = storageService.getString(SPKeys.timezone);

        // 创建新的WebSocket连接
        final newSocket = await WebSocket.connect(
          _url,
          headers: {
            'accept': 'application/json',
            'content-type': 'application/json',
            'GK-platform': getIt<IDeviceInfoService>().platform,
            'GK-app-version': getIt<IPackageInfoService>().version,
            'GK-Timezone': timezone,
            'Authorization': 'Bearer $accessToken',
          },
        );

        // 只有在连接成功后才设置socket引用和状态
        _socket = newSocket;
        _socket!.listen(
          handleMessage,
          onError: handleError,
          onDone: handleDisconnection,
          cancelOnError: false,
        );

        // 设置连接状态
        _isConnected = true;
        _reconnectAttempts = 0;

        // 启动心跳检测
        startHeartbeat();

        LogUtils.i('WebSocket连接成功建立: $_url',
            tag: 'WebSocketDataSourceImpl._establishConnection');
        return Either.right(null);
      } finally {
        // 无论成功还是失败，都要释放连接状态锁
        _isConnecting = false;
      }
    } on WebSocketException catch (e) {
      // 连接失败时清理状态
      _isConnected = false;
      _isConnecting = false;
      _socket = null; // 确保socket引用被清空

      final error = AppException(
        statusCode: 1006,
        message: 'WebSocket connection failed: ${e.message}',
        identifier: 'WS_CONNECTION_ERROR',
      );
      LogUtils.e(
        'WebSocket连接异常',
        error: error,
        tag: 'WebSocketDataSourceImpl._establishConnection',
      );
      return Either.left(error);
    } catch (e) {
      // 其他异常时清理状态
      _isConnected = false;
      _isConnecting = false;
      _socket = null; // 确保socket引用被清空

      final error = AppException(
        statusCode: 500,
        message: 'Unexpected WebSocket error: $e',
        identifier: 'WS_UNEXPECTED_ERROR',
      );
      LogUtils.e(
        'WebSocket意外错误',
        error: error,
        tag: 'WebSocketDataSourceImpl._establishConnection',
      );
      return Either.left(error);
    }
  }

  void handleMessage(dynamic message) {
    if (jsonDecode(message)['msg'] == 'pong') {
      _lastHeartbeatResponse = DateTime.now();
      LogUtils.d(message, tag: 'heartBeat.$hashCode');
      return;
    }

    try {
      LogUtils.d('onMessage: ${message.toString()}',
          tag: 'WebSocketRepositoryImpl._setupMessageListener');
      final resp =
          response.WebsocketResponse.fromJson(jsonDecode(message ?? '{}'));

      _messageController.add(resp);
    } catch (e) {
      LogUtils.e(e.toString(),
          tag: 'WebSocketRepositoryImpl._setupMessageListener.error');
    }
  }

  void handleError(dynamic error) {
    final exception = error is AppException
        ? error
        : AppException(
            statusCode: 1006,
            message: error.toString(),
            identifier: 'WS_ERROR',
          );

    LogUtils.e('WebSocket错误: ${exception.message}',
        tag: 'WebSocketDataSourceImpl.handleError');

    // 清理连接状态和引用
    _isConnected = false;
    _isConnecting = false;
    _socket = null; // 清空socket引用，防止后续操作使用无效连接

    // 如果不是在dispose过程中，才添加错误到流中并尝试重连
    if (!isDisposing) {
      _messageController.addError(exception);
      // 尝试刷新token并重连
      unawaited(tryRefreshTokenAndReconnect());
    }
  }

  void handleDisconnection() {
    final closeCode = _socket?.closeCode;
    LogUtils.i(
      'WebSocket连接已断开: closeCode=$closeCode, url=$url, hashCode=$hashCode',
      tag: 'WebSocketDataSourceImpl.handleDisconnection',
    );

    // 清理连接状态
    _isConnected = false;
    _isConnecting = false;

    // 如果正在dispose，不进行任何重连操作
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过重连逻辑',
          tag: 'WebSocketDataSourceImpl.handleDisconnection');
      _socket = null; // 清空socket引用
      return;
    }

    // 检查是否为客户端主动断开的连接
    if ([
      CloseCode.initial.value,
      CloseCode.goingAway.value,
    ].contains(closeCode)) {
      LogUtils.d('客户端主动断开连接，停止心跳但不重连',
          tag: 'WebSocketDataSourceImpl.handleDisconnection');
      stopHeartbeat();
      _socket = null; // 清空socket引用
      return;
    }

    // 清空socket引用
    _socket = null;

    // 对于所有非客户端主动断开的连接，都尝试重连
    LogUtils.d('非主动断开连接，启动重连机制',
        tag: 'WebSocketDataSourceImpl.handleDisconnection');
    unawaited(tryRefreshTokenAndReconnect());
  }

  @override
  Future<void> tryRefreshTokenAndReconnect() async {
    // 如果正在dispose，不进行重连
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过重连',
          tag: 'WebSocketDataSourceImpl.tryRefreshTokenAndReconnect');
      return;
    }

    // 如果已有活跃的重连任务，等待其完成
    if (_activeReconnectTask != null) {
      LogUtils.d('重连任务已在进行中，等待完成',
          tag: 'WebSocketDataSourceImpl.tryRefreshTokenAndReconnect');
      await _activeReconnectTask;
      return;
    }

    // 创建新的重连任务
    _activeReconnectTask = performReconnect();

    try {
      await _activeReconnectTask;
    } finally {
      _activeReconnectTask = null;
    }
  }

  Future<void> performReconnect() async {
    // 如果正在dispose，不进行重连
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过token刷新和重连',
          tag: 'WebSocketDataSourceImpl._performReconnect');
      return;
    }

    final result = await getIt<TokenRefreshService>().refreshToken();
    result.fold((left) {
      LogUtils.e('token刷新失败', tag: 'WebSocketDataSourceImpl._performReconnect');
      // token刷新失败时，仍然尝试重连
      initiateReconnection();
    }, (right) {
      LogUtils.d('token刷新成功，正在重连...',
          tag: 'WebSocketDataSourceImpl._performReconnect');

      // 重置重连尝试计数，让重连立即执行
      _reconnectAttempts = 0;
      initiateReconnection();
    });
  }

  void initiateReconnection() {
    // 如果正在dispose，不进行重连
    if (isDisposing) {
      LogUtils.d('WebSocket正在dispose，跳过重连调度',
          tag: 'WebSocketDataSourceImpl._initiateReconnection');
      return;
    }

    _reconnectTimer?.cancel();

    // Calculate delay using exponential backoff
    final delay = min(
      initialReconnectDelay * pow(2, _reconnectAttempts),
      maxReconnectDelay,
    ).toInt();

    LogUtils.i(
      'Scheduling reconnection attempt ${_reconnectAttempts + 1} in $delay seconds',
      tag: 'WebSocketDataSourceImpl._initiateReconnection',
    );

    _reconnectTimer = Timer(Duration(seconds: delay), () async {
      // 在定时器回调中再次检查dispose状态
      if (isDisposing) {
        LogUtils.d('WebSocket正在dispose，取消重连尝试',
            tag: 'WebSocketDataSourceImpl._initiateReconnection');
        return;
      }

      _reconnectAttempts++;

      final result = await _establishConnection(
        closeCode: CloseCode.normal,
      );
      result.fold(
        (error) {
          LogUtils.e(
            'Reconnection attempt $_reconnectAttempts failed: ${error.message}',
            tag: 'WebSocketDataSourceImpl._initiateReconnection',
          );

          if (error.identifier == 'WS_NO_SESSION_INFO') {
            _reconnectTimer?.cancel();
            return;
          }

          initiateReconnection();
        },
        (_) {
          LogUtils.i(
            'Reconnection successful after $_reconnectAttempts attempts',
            tag: 'WebSocketDataSourceImpl._initiateReconnection',
          );
          _reconnectAttempts = 0;

          // 重连成功后启动心跳
          startHeartbeat();
        },
      );
    });
  }

  @override
  Future<ResultWithData<void>> disconnect(
      {required CloseCode closeCode}) async {
    LogUtils.d('开始断开WebSocket连接，closeCode: ${closeCode.value}',
        tag: 'WebSocketDataSourceImpl.disconnect');

    // 停止心跳和重连定时器
    stopHeartbeat();
    _reconnectTimer?.cancel();

    // 保存当前socket引用，避免在关闭过程中被其他操作修改
    final currentSocket = _socket;

    // 立即重置状态，防止其他操作认为连接仍然有效
    _isConnected = false;
    _isConnecting = false;
    _reconnectAttempts = 0;
    _socket = null; // 清空socket引用

    // 如果没有socket，直接返回成功
    if (currentSocket == null) {
      LogUtils.d('没有活跃的WebSocket连接需要断开',
          tag: 'WebSocketDataSourceImpl.disconnect');
      return Either.right(null);
    }

    try {
      // 尝试优雅关闭连接
      await currentSocket.close(closeCode.value);
      LogUtils.i('WebSocket连接已优雅断开', tag: 'WebSocketDataSourceImpl.disconnect');
      return Either.right(null);
    } catch (e) {
      // 即使关闭失败，也要确保状态已重置
      LogUtils.w('WebSocket关闭时发生异常，但状态已重置: ${e.toString()}',
          tag: 'WebSocketDataSourceImpl.disconnect');

      // 对于关闭异常，我们仍然认为断开操作成功
      // 因为状态已经重置，连接已经不可用
      return Either.right(null);
    }
  }

  @override
  Stream<response.WebsocketResponse> get messageStream {
    return _messageController.stream;
  }

  /// 检查连接是否有效
  bool _isConnectionValid() {
    return _isConnected && _socket != null && !isDisposing;
  }

  @override
  Future<ResultWithData<void>> sendMessage(
      WebSocketMessageEntity message) async {
    // 检查连接状态
    if (!_isConnectionValid()) {
      LogUtils.w('尝试发送消息但WebSocket未连接',
          tag: 'WebSocketDataSourceImpl.sendMessage');
      return Either.left(const AppException(
        statusCode: 1006,
        message: 'WebSocket is not connected',
        identifier: 'WS_NOT_CONNECTED',
      ));
    }

    try {
      _socket!.add(message.data);
      return Either.right(null);
    } catch (e) {
      LogUtils.e('发送消息失败: $e',
          tag: 'WebSocketDataSourceImpl.sendMessage');

      // 发送失败时处理错误，这可能会触发重连
      handleError(e);

      return Either.left(AppException(
        statusCode: 500,
        message: 'Failed to send message: ${e.toString()}',
        identifier: 'WS_SEND_ERROR',
      ));
    }
  }

  @override
  void startHeartbeat() {
    _heartbeatTimer?.cancel();
    _lastHeartbeatResponse = DateTime.now(); // Initialize last response time
    _heartbeatTimer = Timer.periodic(
      const Duration(seconds: heartbeatInterval),
      (_) => sendHeartbeat(),
    );

    // Immediately send a heartbeat to verify connection
    sendHeartbeat();
  }

  @override
  void stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  void sendHeartbeat() async {
    // 检查连接是否有效
    if (!_isConnectionValid()) {
      LogUtils.d('连接无效，跳过心跳发送', tag: 'WebSocketDataSourceImpl.sendHeartbeat');
      return;
    }

    try {
      // 检查心跳响应超时
      if (_lastHeartbeatResponse != null) {
        final elapsed = DateTime.now().difference(_lastHeartbeatResponse!);
        if (elapsed.inSeconds > heartbeatInterval * 2) {
          LogUtils.w(
            '心跳响应超时 ${elapsed.inSeconds} 秒，连接可能已断开: $url',
            tag: 'WebSocketDataSourceImpl.sendHeartbeat',
          );
          // 连接可能已死，触发断开处理
          handleDisconnection();
          return;
        }
      }

      // 发送心跳消息
      final result = await sendMessage(WebSocketMessageEntity(
        data: heartbeatMessage,
        type: WebSocketMessageType.json,
      ));

      // 检查发送结果
      result.fold(
        (error) {
          LogUtils.w('心跳发送失败: ${error.message}',
              tag: 'WebSocketDataSourceImpl.sendHeartbeat');
        },
        (_) {
          // 心跳发送成功，无需额外处理
        },
      );
    } catch (e) {
      LogUtils.e(
        '心跳发送异常: $e',
        tag: 'WebSocketDataSourceImpl.sendHeartbeat',
      );
      handleError(e);
    }
  }

  @override
  void resetReconnectionState() {
    LogUtils.i(
      'Resetting reconnection state',
      tag: 'WebSocketDataSourceImpl.resetReconnectionState',
    );
    _reconnectAttempts = 0;
    _reconnectTimer?.cancel();

    // 注意：不直接设置为null，而是让正在进行的任务自然完成
    // 这样可以避免任务泄漏和竞态条件
    // _activeReconnectTask 和 _activeConnectTask 会在各自的finally块中被清理

    // 重置连接状态锁，允许新的连接尝试
    _isConnecting = false;
  }

  // 添加标志位来标识是否正在dispose
  bool isDisposing = false;

  /// 添加dispose方法，用于释放资源
  /// 应在使用此服务的对象被销毁时调用
  @override
  Future<void> dispose() async {
    // 设置dispose标志，防止重连
    isDisposing = true;

    stopHeartbeat();
    _reconnectTimer?.cancel();

    // 等待活跃的任务完成，避免任务泄漏
    if (_activeReconnectTask != null) {
      try {
        await _activeReconnectTask;
      } catch (e) {
        LogUtils.w('Error waiting for reconnect task to complete: $e',
            tag: 'WebSocketDataSourceImpl.dispose');
      }
    }

    if (_activeConnectTask != null) {
      try {
        await _activeConnectTask;
      } catch (e) {
        LogUtils.w('Error waiting for connect task to complete: $e',
            tag: 'WebSocketDataSourceImpl.dispose');
      }
    }

    // 清理任务引用和状态
    _activeReconnectTask = null;
    _activeConnectTask = null;
    _isConnecting = false;
    _isConnected = false;

    // 关闭消息流
    await _messageController.close();

    // 最后关闭WebSocket连接
    await _socket?.close(CloseCode.goingAway.value);
  }
}
